import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
  Alert,
  Fab,
  useTheme,
  useMediaQuery,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  VideoLibrary,
  People,
  School,
  PlayCircle,
  Save,
  Cancel,
  CloudUpload,
  PictureAsPdf,
  AttachFile
} from '@mui/icons-material';
import toast from 'react-hot-toast';

// استيراد خدمة التكامل المختلطة
import { hybridCourses } from '../../services/hybridDatabaseService';

// استيراد مكونات رفع الملفات الجديدة
import VideoUploadManager from './VideoUploadManager';
import PDFUploadManager from './PDFUploadManager';

const CourseManagement = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));
  const isSmallMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingCourse, setEditingCourse] = useState(null);

  // حالات رفع الملفات الجديدة
  const [openVideoUpload, setOpenVideoUpload] = useState(false);
  const [openPDFUpload, setOpenPDFUpload] = useState(false);
  const [selectedCourseForUpload, setSelectedCourseForUpload] = useState(null);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    instructor: 'علاء عبد الحميد',
    duration: '',
    level: 'مبتدئ',
    price: 0,
    isActive: true,
    totalVideos: 0,
    enrolledStudents: 0,
    videos: [],
    documents: [] // إضافة مصفوفة للمستندات
  });

  // جلب الكورسات من النظام المختلط مع التحديث الفوري
  useEffect(() => {
    console.log('🔄 بدء مراقبة الكورسات المختلطة...');

    const unsubscribe = hybridCourses.watchCourses((coursesData) => {
      setCourses(coursesData);
      console.log('✅ تم تحديث الكورسات:', coursesData.length);
    });

    return () => {
      console.log('🛑 إيقاف مراقبة الكورسات');
      unsubscribe();
    };
  }, []);

  // إضافة كورس جديد
  const handleAddCourse = async () => {
    if (!formData.title.trim() || !formData.description.trim()) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    setLoading(true);
    try {
      // استخدام الخدمة المختلطة لإضافة الكورس
      const result = await hybridCourses.addCourse({
        ...formData,
        instructor: 'علاء عبد الحميد',
        level: formData.level || 'مبتدئ',
        duration: formData.duration || '4 أسابيع',
        price: formData.price || 0,
        thumbnail: formData.thumbnail || '/default-course.jpg',
        tags: formData.tags || [],
        isPublished: true
      });

      if (result.success) {
        console.log('✅ تم إضافة الكورس مع تحديث فوري:', result.id);
        toast.success('تم إضافة الكورس بنجاح - سيظهر فوراً للطلاب');
        handleCloseDialog();
      }
    } catch (error) {
      console.error('❌ خطأ في إضافة الكورس:', error);
      toast.error('فشل في إضافة الكورس');
    } finally {
      setLoading(false);
    }
  };

  // تحديث كورس موجود
  const handleUpdateCourse = async () => {
    if (!editingCourse || !formData.title.trim() || !formData.description.trim()) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    setLoading(true);
    try {
      const result = await hybridCourses.updateCourse(editingCourse.id, formData);

      if (result.success) {
        console.log('✅ تم تحديث الكورس:', editingCourse.id);
        toast.success('تم تحديث الكورس بنجاح');
        handleCloseDialog();
      }
    } catch (error) {
      console.error('❌ خطأ في تحديث الكورس:', error);
      toast.error('فشل في تحديث الكورس');
    } finally {
      setLoading(false);
    }
  };

  // حذف كورس
  const handleDeleteCourse = async (courseId, courseTitle) => {
    if (!window.confirm(`هل أنت متأكد من حذف الكورس "${courseTitle}"؟`)) {
      return;
    }

    setLoading(true);
    try {
      const result = await hybridCourses.deleteCourse(courseId);

      if (result.success) {
        console.log('✅ تم حذف الكورس:', courseId);
        toast.success('تم حذف الكورس بنجاح');
      }
    } catch (error) {
      console.error('❌ خطأ في حذف الكورس:', error);
      toast.error('فشل في حذف الكورس');
    } finally {
      setLoading(false);
    }
  };

  // تبديل حالة الكورس (مفعل/غير مفعل)
  const handleToggleCourseStatus = async (courseId, currentStatus) => {
    try {
      const result = await hybridCourses.updateCourse(courseId, {
        is_active: !currentStatus
      });

      if (result.success) {
        toast.success(`تم ${!currentStatus ? 'تفعيل' : 'إلغاء تفعيل'} الكورس`);
      }
    } catch (error) {
      console.error('❌ خطأ في تغيير حالة الكورس:', error);
      toast.error('فشل في تغيير حالة الكورس');
    }
  };

  // فتح مدير رفع الفيديوهات
  const handleOpenVideoUpload = (course) => {
    setSelectedCourseForUpload(course);
    setOpenVideoUpload(true);
  };

  // فتح مدير رفع ملفات PDF
  const handleOpenPDFUpload = (course) => {
    setSelectedCourseForUpload(course);
    setOpenPDFUpload(true);
  };

  // معالج رفع فيديو جديد
  const handleVideoUploaded = async (videoData) => {
    try {
      // إضافة الفيديو إلى قاعدة البيانات
      const result = await hybridCourses.addVideoToCourse(
        selectedCourseForUpload.id,
        videoData
      );

      if (result.success) {
        toast.success('تم إضافة الفيديو بنجاح');

        // تحديث عدد الفيديوهات في الكورس
        await hybridCourses.updateCourse(selectedCourseForUpload.id, {
          total_videos: (selectedCourseForUpload.total_videos || 0) + 1
        });
      }
    } catch (error) {
      console.error('❌ خطأ في إضافة الفيديو:', error);
      toast.error('فشل في إضافة الفيديو إلى قاعدة البيانات');
    }
  };

  // معالج رفع ملف PDF جديد
  const handlePDFUploaded = async (pdfData) => {
    try {
      // إضافة ملف PDF إلى قاعدة البيانات
      const result = await hybridCourses.addDocumentToCourse(
        selectedCourseForUpload.id,
        pdfData
      );

      if (result.success) {
        toast.success('تم إضافة الملف بنجاح');
      }
    } catch (error) {
      console.error('❌ خطأ في إضافة الملف:', error);
      toast.error('فشل في إضافة الملف إلى قاعدة البيانات');
    }
  };

  // فتح نافذة إضافة كورس جديد
  const handleOpenAddDialog = () => {
    setEditingCourse(null);
    setFormData({
      title: '',
      description: '',
      instructor: 'علاء عبد الحميد',
      duration: '',
      level: 'مبتدئ',
      price: 0,
      isActive: true,
      totalVideos: 0,
      enrolledStudents: 0,
      videos: []
    });
    setOpenDialog(true);
  };

  // فتح نافذة تعديل كورس
  const handleOpenEditDialog = (course) => {
    setEditingCourse(course);
    setFormData({
      title: course.title || '',
      description: course.description || '',
      instructor: course.instructor || 'علاء عبد الحميد',
      duration: course.duration || '',
      level: course.level || 'مبتدئ',
      price: course.price || 0,
      isActive: course.isActive !== undefined ? course.isActive : true,
      totalVideos: course.totalVideos || 0,
      enrolledStudents: course.enrolledStudents || 0,
      videos: course.videos || []
    });
    setOpenDialog(true);
  };

  // إغلاق النافذة
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingCourse(null);
    setFormData({
      title: '',
      description: '',
      instructor: 'علاء عبد الحميد',
      duration: '',
      level: 'مبتدئ',
      price: 0,
      isActive: true,
      totalVideos: 0,
      enrolledStudents: 0,
      videos: []
    });
  };

  // تحديث بيانات النموذج
  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // تنسيق التاريخ
  const formatDate = (date) => {
    if (!date) return 'غير محدد';
    if (date.toDate) date = date.toDate();
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: { xs: 'flex-start', sm: 'center' },
          flexDirection: { xs: 'column', sm: 'row' },
          gap: { xs: 2, sm: 0 },
          mb: 3
        }}
      >
        <Typography
          variant="h4"
          component="h1"
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.125rem' },
            fontWeight: { xs: 600, md: 700 }
          }}
        >
          <School
            color="primary"
            sx={{ fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2rem' } }}
          />
          إدارة الكورسات
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={handleOpenAddDialog}
          size={isMobile ? "medium" : "large"}
          sx={{
            borderRadius: 2,
            fontSize: { xs: '0.8rem', sm: '0.875rem', md: '1rem' },
            px: { xs: 2, sm: 3, md: 4 },
            py: { xs: 1, sm: 1.2, md: 1.5 },
            minWidth: { xs: '140px', sm: '160px' },
            alignSelf: { xs: 'stretch', sm: 'auto' }
          }}
        >
          {isSmallMobile ? 'إضافة كورس' : 'إضافة كورس جديد'}
        </Button>
      </Box>

      {/* إحصائيات سريعة */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="primary">{courses.length}</Typography>
            <Typography variant="body2" color="text.secondary">إجمالي الكورسات</Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="success.main">
              {courses.filter(c => c.isActive).length}
            </Typography>
            <Typography variant="body2" color="text.secondary">الكورسات المفعلة</Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="info.main">
              {courses.reduce((total, course) => total + (course.totalVideos || 0), 0)}
            </Typography>
            <Typography variant="body2" color="text.secondary">إجمالي الفيديوهات</Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="warning.main">
              {courses.reduce((total, course) => total + (course.enrolledStudents || 0), 0)}
            </Typography>
            <Typography variant="body2" color="text.secondary">إجمالي التسجيلات</Typography>
          </Card>
        </Grid>
      </Grid>

      {/* جدول الكورسات */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            قائمة الكورسات ({courses.length})
          </Typography>
          
          {loading && (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress />
            </Box>
          )}

          {!loading && courses.length === 0 && (
            <Alert severity="info" sx={{ mt: 2 }}>
              لا توجد كورسات حالياً. اضغط على "إضافة كورس جديد" لإنشاء أول كورس.
            </Alert>
          )}

          {!loading && courses.length > 0 && (
            <>
              {/* عرض الجدول للشاشات الكبيرة */}
              <TableContainer
                component={Paper}
                sx={{
                  mt: 2,
                  display: { xs: 'none', lg: 'block' },
                  overflowX: 'auto'
                }}
              >
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>عنوان الكورس</TableCell>
                      <TableCell>المدرب</TableCell>
                      <TableCell>المستوى</TableCell>
                      <TableCell>المدة</TableCell>
                      <TableCell>الفيديوهات</TableCell>
                      <TableCell>الطلاب</TableCell>
                      <TableCell>الحالة</TableCell>
                      <TableCell>تاريخ الإنشاء</TableCell>
                      <TableCell>الإجراءات</TableCell>
                    </TableRow>
                  </TableHead>
                <TableBody>
                  {courses.map((course) => (
                    <TableRow key={course.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2" fontWeight="bold">
                            {course.title}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {course.description?.substring(0, 50)}...
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{course.instructor}</TableCell>
                      <TableCell>
                        <Chip 
                          label={course.level} 
                          size="small"
                          color={
                            course.level === 'مبتدئ' ? 'success' :
                            course.level === 'متوسط' ? 'warning' : 'error'
                          }
                        />
                      </TableCell>
                      <TableCell>{course.duration}</TableCell>
                      <TableCell>
                        <Chip 
                          icon={<PlayCircle />}
                          label={course.totalVideos || 0}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          icon={<People />}
                          label={course.enrolledStudents || 0}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={course.isActive}
                              onChange={() => handleToggleCourseStatus(course.id, course.isActive)}
                              size="small"
                            />
                          }
                          label={course.isActive ? 'مفعل' : 'غير مفعل'}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="caption">
                          {formatDate(course.createdAt)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" gap={1} flexWrap="wrap">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenEditDialog(course)}
                            color="primary"
                            title="تعديل الكورس"
                          >
                            <Edit />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleOpenVideoUpload(course)}
                            color="secondary"
                            title="رفع فيديوهات"
                            sx={{
                              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                              color: 'white',
                              '&:hover': {
                                background: 'linear-gradient(135deg, #764ba2 0%, #667eea 100%)'
                              }
                            }}
                          >
                            <CloudUpload />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleOpenPDFUpload(course)}
                            color="error"
                            title="رفع ملفات PDF"
                            sx={{
                              background: 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)',
                              color: 'white',
                              '&:hover': {
                                background: 'linear-gradient(135deg, #c0392b 0%, #a93226 100%)'
                              }
                            }}
                          >
                            <PictureAsPdf />
                          </IconButton>
                          <IconButton
                            size="small"
                            color="info"
                            title="إدارة المحتوى"
                          >
                            <VideoLibrary />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteCourse(course.id, course.title)}
                            color="error"
                            title="حذف الكورس"
                          >
                            <Delete />
                          </IconButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            {/* عرض Cards للأجهزة المحمولة والأجهزة اللوحية */}
            <Box sx={{ display: { xs: 'block', lg: 'none' }, mt: 2 }}>
              <Grid container spacing={2}>
                {courses.map((course) => (
                  <Grid item xs={12} sm={6} md={4} key={course.id}>
                    <Card
                      sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        '&:hover': {
                          boxShadow: 3,
                          transform: 'translateY(-2px)',
                          transition: 'all 0.3s ease'
                        }
                      }}
                    >
                      <CardContent sx={{ flexGrow: 1 }}>
                        <Typography
                          variant="h6"
                          gutterBottom
                          sx={{
                            fontSize: { xs: '1rem', sm: '1.1rem' },
                            fontWeight: 'bold',
                            lineHeight: 1.3
                          }}
                        >
                          {course.title}
                        </Typography>

                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ mb: 2, fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                        >
                          {course.description?.substring(0, 80)}...
                        </Typography>

                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                          <Chip
                            label={course.level}
                            size="small"
                            color={
                              course.level === 'مبتدئ' ? 'success' :
                              course.level === 'متوسط' ? 'warning' : 'error'
                            }
                          />
                          <Chip
                            icon={<PlayCircle />}
                            label={`${course.totalVideos || 0} فيديو`}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            icon={<People />}
                            label={`${course.enrolledStudents || 0} طالب`}
                            size="small"
                            variant="outlined"
                          />
                        </Box>

                        <Typography variant="caption" color="text.secondary" display="block">
                          المدرب: {course.instructor}
                        </Typography>
                        <Typography variant="caption" color="text.secondary" display="block">
                          المدة: {course.duration}
                        </Typography>
                      </CardContent>

                      <Box sx={{ p: 2, pt: 0 }}>
                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                          <Button
                            size="small"
                            variant="outlined"
                            startIcon={<CloudUpload />}
                            onClick={() => handleOpenVideoUpload(course)}
                            sx={{ fontSize: { xs: '0.7rem', sm: '0.8rem' } }}
                          >
                            فيديو
                          </Button>
                          <Button
                            size="small"
                            variant="outlined"
                            startIcon={<PictureAsPdf />}
                            onClick={() => handleOpenPDFUpload(course)}
                            sx={{ fontSize: { xs: '0.7rem', sm: '0.8rem' } }}
                          >
                            PDF
                          </Button>
                          <IconButton
                            size="small"
                            onClick={() => handleOpenEditDialog(course)}
                            color="primary"
                          >
                            <Edit fontSize="small" />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteCourse(course.id, course.title)}
                            color="error"
                          >
                            <Delete fontSize="small" />
                          </IconButton>
                        </Box>
                      </Box>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
            </>
          )}
        </CardContent>
      </Card>

      {/* نافذة إضافة/تعديل الكورس */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        fullScreen={isSmallMobile}
        aria-labelledby="course-dialog-title"
        aria-describedby="course-dialog-description"
        sx={{
          '& .MuiDialog-paper': {
            margin: { xs: 1, sm: 2 },
            width: { xs: 'calc(100% - 16px)', sm: 'auto' },
            maxHeight: { xs: 'calc(100% - 16px)', sm: '90vh' }
          }
        }}
      >
        <DialogTitle
          id="course-dialog-title"
          sx={{
            fontSize: { xs: '1.1rem', sm: '1.25rem' },
            fontWeight: 600,
            pb: { xs: 1, sm: 2 }
          }}
        >
          {editingCourse ? 'تعديل الكورس' : 'إضافة كورس جديد'}
        </DialogTitle>
        <DialogContent
          id="course-dialog-description"
          sx={{
            px: { xs: 2, sm: 3 },
            py: { xs: 1, sm: 2 }
          }}
        >
          <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mt: 0.5 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="عنوان الكورس *"
                value={formData.title}
                onChange={(e) => handleFormChange('title', e.target.value)}
                variant="outlined"
                size={isSmallMobile ? "small" : "medium"}
                sx={{
                  '& .MuiInputBase-root': {
                    fontSize: { xs: '0.9rem', sm: '1rem' }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="وصف الكورس *"
                value={formData.description}
                onChange={(e) => handleFormChange('description', e.target.value)}
                variant="outlined"
                multiline
                rows={isSmallMobile ? 2 : 3}
                size={isSmallMobile ? "small" : "medium"}
                sx={{
                  '& .MuiInputBase-root': {
                    fontSize: { xs: '0.9rem', sm: '1rem' }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="المدرب"
                value={formData.instructor}
                onChange={(e) => handleFormChange('instructor', e.target.value)}
                variant="outlined"
                size={isSmallMobile ? "small" : "medium"}
                sx={{
                  '& .MuiInputBase-root': {
                    fontSize: { xs: '0.9rem', sm: '1rem' }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="مدة الكورس"
                value={formData.duration}
                onChange={(e) => handleFormChange('duration', e.target.value)}
                variant="outlined"
                placeholder="مثال: 4 ساعات"
                size={isSmallMobile ? "small" : "medium"}
                sx={{
                  '& .MuiInputBase-root': {
                    fontSize: { xs: '0.9rem', sm: '1rem' }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>المستوى</InputLabel>
                <Select
                  value={formData.level}
                  onChange={(e) => handleFormChange('level', e.target.value)}
                  label="المستوى"
                >
                  <MenuItem value="مبتدئ">مبتدئ</MenuItem>
                  <MenuItem value="متوسط">متوسط</MenuItem>
                  <MenuItem value="متقدم">متقدم</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="السعر"
                type="number"
                value={formData.price}
                onChange={(e) => handleFormChange('price', Number(e.target.value))}
                variant="outlined"
                InputProps={{
                  endAdornment: 'ريال'
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => handleFormChange('isActive', e.target.checked)}
                  />
                }
                label="كورس مفعل"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            px: { xs: 2, sm: 3 },
            py: { xs: 1.5, sm: 2 },
            gap: { xs: 1, sm: 2 },
            flexDirection: { xs: 'column-reverse', sm: 'row' }
          }}
        >
          <Button
            onClick={handleCloseDialog}
            startIcon={<Cancel />}
            aria-label="إلغاء العملية"
            size={isSmallMobile ? "medium" : "large"}
            sx={{
              width: { xs: '100%', sm: 'auto' },
              minWidth: { xs: 'auto', sm: '100px' },
              fontSize: { xs: '0.9rem', sm: '1rem' }
            }}
          >
            إلغاء
          </Button>
          <Button
            onClick={editingCourse ? handleUpdateCourse : handleAddCourse}
            variant="contained"
            startIcon={<Save />}
            disabled={loading}
            aria-label={editingCourse ? 'تحديث الكورس' : 'إضافة كورس جديد'}
            size={isSmallMobile ? "medium" : "large"}
            sx={{
              width: { xs: '100%', sm: 'auto' },
              minWidth: { xs: 'auto', sm: '120px' },
              fontSize: { xs: '0.9rem', sm: '1rem' }
            }}
          >
            {loading ? <CircularProgress size={20} /> : (editingCourse ? 'تحديث' : 'إضافة')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* مدير رفع الفيديوهات */}
      <VideoUploadManager
        open={openVideoUpload}
        onClose={() => {
          setOpenVideoUpload(false);
          setSelectedCourseForUpload(null);
        }}
        courseId={selectedCourseForUpload?.id}
        onVideoUploaded={handleVideoUploaded}
        existingVideos={selectedCourseForUpload?.videos || []}
      />

      {/* مدير رفع ملفات PDF */}
      <PDFUploadManager
        open={openPDFUpload}
        onClose={() => {
          setOpenPDFUpload(false);
          setSelectedCourseForUpload(null);
        }}
        courseId={selectedCourseForUpload?.id}
        onPDFUploaded={handlePDFUploaded}
        existingPDFs={selectedCourseForUpload?.documents || []}
      />
    </Box>
  );
};

export default CourseManagement;
