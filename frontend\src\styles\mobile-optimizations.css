/* تحسينات الأداء للأجهزة المحمولة */

/* تحسين عام للأجهزة المحمولة */
@media (max-width: 768px) {
  /* تحسين الخلفيات */
  .mobile-optimized {
    background-attachment: scroll !important;
    background-size: 100% 100% !important;
    will-change: opacity !important;
    transform: none !important;
  }
  
  /* تحسين الانتقالات */
  .mobile-optimized * {
    transition-duration: 0.2s !important;
    transition-timing-function: ease-out !important;
  }
  
  /* تحسين الصور */
  .mobile-optimized img {
    image-rendering: auto !important;
    will-change: auto !important;
  }
  
  /* تحسين الفلاتر */
  .mobile-optimized [style*="blur"] {
    filter: none !important;
    backdrop-filter: blur(4px) !important;
  }
  
  /* تحسين الظلال */
  .mobile-optimized [style*="box-shadow"] {
    box-shadow: 0 4px 12px rgba(0, 0, 255, 0.15) !important;
  }
}

/* تحسينات للأجهزة الصغيرة جداً */
@media (max-width: 480px) {
  .mobile-optimized {
    background: linear-gradient(135deg, rgba(0, 0, 255, 0.02) 0%, rgba(65, 105, 225, 0.03) 100%) !important;
  }
  
  .mobile-optimized [style*="backdrop-filter"] {
    backdrop-filter: blur(2px) !important;
  }
  
  .mobile-optimized [style*="box-shadow"] {
    box-shadow: 0 2px 8px rgba(0, 0, 255, 0.1) !important;
  }
}

/* تحسينات للأجهزة اللوحية */
@media (min-width: 769px) and (max-width: 1024px) {
  .background-loaded {
    background-attachment: scroll !important;
    background-size: cover !important;
  }
  
  .background-loaded [style*="backdrop-filter"] {
    backdrop-filter: blur(8px) saturate(1.2) !important;
  }
}

/* تحسينات الأداء العامة */
.performance-optimized {
  /* تحسين الرسم */
  contain: layout style paint;
  
  /* تحسين التمرير */
  -webkit-overflow-scrolling: touch;
  
  /* تحسين الخطوط */
  text-rendering: optimizeSpeed;
  
  /* تحسين الصور */
  image-rendering: -webkit-optimize-contrast;
}

/* تحسين التحميل السريع */
.instant-load {
  opacity: 1 !important;
  transition: none !important;
  animation: none !important;
}

/* تحسين الذاكرة */
.memory-optimized {
  will-change: auto !important;
  transform: none !important;
  filter: none !important;
}

/* خلفية بسيطة للأجهزة المحمولة */
.mobile-simple-bg {
  background: linear-gradient(135deg, 
    rgba(0, 0, 255, 0.02) 0%, 
    rgba(65, 105, 225, 0.03) 25%,
    rgba(99, 102, 241, 0.02) 50%,
    rgba(65, 105, 225, 0.03) 75%,
    rgba(0, 0, 255, 0.02) 100%) !important;
  background-attachment: scroll !important;
  background-size: 100% 100% !important;
  background-repeat: no-repeat !important;
}

/* تحسين النصوص للأجهزة المحمولة */
@media (max-width: 768px) {
  .mobile-text-optimized {
    font-smooth: never !important;
    -webkit-font-smoothing: subpixel-antialiased !important;
    -moz-osx-font-smoothing: auto !important;
    text-rendering: optimizeSpeed !important;
  }
}

/* تحسين الأزرار للأجهزة اللمسية */
@media (hover: none) and (pointer: coarse) {
  .touch-optimized {
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 12px 16px !important;
  }
  
  .touch-optimized:hover {
    transform: none !important;
    box-shadow: none !important;
  }
  
  .touch-optimized:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease-out !important;
  }
}

/* تحسين الانتقالات للأجهزة البطيئة */
@media (prefers-reduced-motion: reduce) {
  .reduced-motion {
    animation: none !important;
    transition: none !important;
  }
}

/* تحسين استهلاك البطارية */
@media (prefers-reduced-motion: reduce), (max-width: 768px) {
  .battery-optimized {
    animation-play-state: paused !important;
    transition-duration: 0.1s !important;
    will-change: auto !important;
    transform: none !important;
    filter: none !important;
    backdrop-filter: none !important;
  }
}

/* تحسين الشبكة البطيئة */
@media (max-width: 768px) {
  .slow-network-optimized {
    background-image: none !important;
    background: linear-gradient(135deg, rgba(0, 0, 255, 0.02) 0%, rgba(65, 105, 225, 0.03) 100%) !important;
  }
  
  .slow-network-optimized img {
    display: none !important;
  }
  
  .slow-network-optimized [style*="url("] {
    background-image: none !important;
  }
}

/* تحسين الذاكرة المنخفضة */
@media (max-width: 480px) {
  .low-memory-optimized {
    background: #f8f9fa !important;
    box-shadow: none !important;
    border: 1px solid #e9ecef !important;
    backdrop-filter: none !important;
    filter: none !important;
  }
}

/* تحسين الأداء للشاشات عالية الكثافة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .retina-optimized {
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
  }
}

/* تحسين التمرير */
.scroll-optimized {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: auto;
  overscroll-behavior: contain;
}

/* تحسين الإدخال للأجهزة اللمسية */
.input-optimized {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* تحسين الخطوط */
.font-optimized {
  font-display: swap;
  font-variant-ligatures: none;
  font-feature-settings: "kern" 0;
}

/* تحسين الرسوم المتحركة */
@keyframes fadeInFast {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fast-fade-in {
  animation: fadeInFast 0.2s ease-out forwards;
}

/* تحسين التحميل التدريجي */
.progressive-load {
  opacity: 0;
  transition: opacity 0.2s ease-out;
}

.progressive-load.loaded {
  opacity: 1;
}

/* تحسين الشبكة */
@media (max-width: 768px) and (max-bandwidth: 1mbps) {
  .bandwidth-optimized {
    background: #ffffff !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: none !important;
    filter: none !important;
  }
}
